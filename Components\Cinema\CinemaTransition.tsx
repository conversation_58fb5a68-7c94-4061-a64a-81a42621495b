import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { gsap } from 'gsap';

// 🎬 CISCO: Interface pour les volets cinématographiques
interface CinemaTransitionProps {
  children?: React.ReactNode;
}

// 🎬 CISCO: Interface pour les méthodes exposées
export interface CinemaTransitionRef {
  openCurtains: () => Promise<void>;
  closeCurtains: () => Promise<void>;
  resetCurtains: () => void;
}

// 🎬 CISCO: Composant volets cinématographiques avec ouverture ultra-progressive
const CinemaTransition = forwardRef<CinemaTransitionRef, CinemaTransitionProps>(
  ({ children }, ref) => {
    const leftCurtainRef = useRef<HTMLDivElement>(null);
    const rightCurtainRef = useRef<HTMLDivElement>(null);

    // 🎬 CISCO: Ouverture ultra-progressive des volets (10+ secondes)
    const openCurtains = (): Promise<void> => {
      return new Promise((resolve) => {
        console.log('🎬 CISCO: Ouverture volets ultra-progressive (12 secondes)');
        
        const timeline = gsap.timeline({
          onComplete: () => {
            console.log('🎬 CISCO: Volets complètement ouverts');
            resolve();
          }
        });

        // Animation des volets qui glissent vers les côtés (ultra-progressive selon demande CISCO)
        timeline
          .to(leftCurtainRef.current, {
            x: '-100%',
            duration: 12, // 🎬 CISCO: 12 SECONDES - Ultra-progressive et souple
            ease: 'power1.inOut' // Easing très doux pour progression naturelle
          }, 0)
          .to(rightCurtainRef.current, {
            x: '100%',
            duration: 12, // 🎬 CISCO: 12 SECONDES - Ultra-progressive et souple
            ease: 'power1.inOut' // Easing très doux pour progression naturelle
          }, 0);
      });
    };

    // 🎬 CISCO: Fermeture des volets
    const closeCurtains = (): Promise<void> => {
      return new Promise((resolve) => {
        console.log('🎬 CISCO: Fermeture volets');
        
        const timeline = gsap.timeline({
          onComplete: () => {
            console.log('🎬 CISCO: Volets fermés');
            resolve();
          }
        });

        // Retour des volets à leur position initiale
        timeline
          .to(leftCurtainRef.current, {
            x: '0%',
            duration: 3.5,
            ease: 'power2.inOut'
          }, 0)
          .to(rightCurtainRef.current, {
            x: '0%',
            duration: 3.5,
            ease: 'power2.inOut'
          }, 0);
      });
    };

    // 🎬 CISCO: Reset des volets à leur position initiale
    const resetCurtains = () => {
      gsap.set([leftCurtainRef.current, rightCurtainRef.current], {
        x: '0%'
      });
    };

    // 🎬 CISCO: Exposer les méthodes via ref
    useImperativeHandle(ref, () => ({
      openCurtains,
      closeCurtains,
      resetCurtains
    }));

    return (
      <>
        {/* 🎬 CISCO: Volet gauche */}
        <div
          ref={leftCurtainRef}
          className="fixed inset-0 bg-black z-[9999] pointer-events-none"
          style={{
            width: '50%',
            left: '0%',
            borderRight: '2px solid #333',
            boxShadow: '2px 0 10px rgba(0,0,0,0.5)'
          }}
        />

        {/* 🎬 CISCO: Volet droit */}
        <div
          ref={rightCurtainRef}
          className="fixed inset-0 bg-black z-[9999] pointer-events-none"
          style={{
            width: '50%',
            right: '0%',
            borderLeft: '2px solid #333',
            boxShadow: '-2px 0 10px rgba(0,0,0,0.5)'
          }}
        />

        {/* 🎬 CISCO: Contenu affiché derrière les volets */}
        <div className="relative z-10">
          {children}
        </div>
      </>
    );
  }
);

CinemaTransition.displayName = 'CinemaTransition';

export default CinemaTransition;
